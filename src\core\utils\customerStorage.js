/**
 * Utility functions for managing customers with API integration and local storage fallback
 */

import customerService from '../services/customer.service';

// Local storage keys
const CUSTOMERS_STORAGE_KEY = 'pos_customers';
const SELECTED_CUSTOMER_KEY = 'pos_selected_customer';
const CUSTOMER_ORDERS_KEY = 'pos_customer_orders';
const CUSTOMERS_CACHE_KEY = 'pos_customers_cache';
const CACHE_EXPIRY_KEY = 'pos_customers_cache_expiry';
const PENDING_SYNC_KEY = 'pos_customers_pending_sync';
const SYNC_STATUS_KEY = 'pos_customers_sync_status';

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Check if cache is valid
 * @returns {boolean} True if cache is valid
 */
const isCacheValid = () => {
  try {
    const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);
    if (!expiry) return false;

    const expiryTime = parseInt(expiry, 10);
    return Date.now() < expiryTime;
  } catch (error) {
    console.error('Error checking cache validity:', error);
    return false;
  }
};

/**
 * Get customers from cache
 * @returns {Array|null} Cached customers or null if cache is invalid
 */
const getCachedCustomers = () => {
  try {
    if (!isCacheValid()) return null;

    const cached = localStorage.getItem(CUSTOMERS_CACHE_KEY);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.error('Error retrieving cached customers:', error);
    return null;
  }
};

/**
 * Cache customers data
 * @param {Array} customers - Customers to cache
 */
const cacheCustomers = (customers) => {
  try {
    localStorage.setItem(CUSTOMERS_CACHE_KEY, JSON.stringify(customers));
    localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());
  } catch (error) {
    console.error('Error caching customers:', error);
  }
};

/**
 * Get all customers from API with local storage fallback
 * @param {boolean} forceRefresh - Force refresh from API
 * @returns {Promise<Array>} Array of customer objects
 */
export const getCustomers = async (forceRefresh = false) => {
  try {
    // Check cache first if not forcing refresh
    if (!forceRefresh) {
      const cached = getCachedCustomers();
      if (cached) {
        console.log('Using cached customers');
        return cached;
      }
    }

    // Try to fetch from API
    console.log('Fetching customers from API...');
    const response = await customerService.getCustomers({ page: 1, pageSize: 100 });

    if (response && response.result) {
      // Transform backend data to frontend format
      const customers = response.result.map(customer => {
        const transformedCustomer = customerService.transformToFrontendFormat(customer);

        // Debug: Check for nested objects in transformed customer
        Object.keys(transformedCustomer).forEach(key => {
          if (typeof transformedCustomer[key] === 'object' && transformedCustomer[key] !== null && !Array.isArray(transformedCustomer[key])) {
            console.warn(`Removing nested object from transformed customer: ${key}`, transformedCustomer[key]);
            delete transformedCustomer[key];
          }
        });

        return transformedCustomer;
      });

      // Always include default walk-in customer
      const defaultCustomers = getDefaultCustomers();
      const allCustomers = [...defaultCustomers, ...customers];

      // Cache the results
      cacheCustomers(allCustomers);

      // Also save to local storage for offline access
      saveCustomers(allCustomers);

      return allCustomers;
    }

    // Fallback to local storage if API fails
    console.log('API failed, falling back to local storage');
    return getCustomersFromLocalStorage();

  } catch (error) {
    console.error('Error fetching customers from API:', error);

    // Fallback to local storage
    return getCustomersFromLocalStorage();
  }
};

/**
 * Get customers from local storage only (original function)
 * @returns {Array} Array of customer objects
 */
export const getCustomersFromLocalStorage = () => {
  try {
    const customers = localStorage.getItem(CUSTOMERS_STORAGE_KEY);
    if (customers) {
      const parsedCustomers = JSON.parse(customers);

      // Clean any nested objects from existing data
      const cleanedCustomers = parsedCustomers.map(customer => {
        const cleaned = { ...customer };
        Object.keys(cleaned).forEach(key => {
          if (typeof cleaned[key] === 'object' && cleaned[key] !== null && !Array.isArray(cleaned[key])) {
            console.warn(`Removing nested object from stored customer.${key}:`, cleaned[key]);
            delete cleaned[key];
          }
        });
        return cleaned;
      });

      // Save cleaned data back to localStorage
      if (cleanedCustomers.length !== parsedCustomers.length ||
          JSON.stringify(cleanedCustomers) !== JSON.stringify(parsedCustomers)) {
        console.log('Cleaned customer data, saving back to localStorage');
        localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(cleanedCustomers));
      }

      return cleanedCustomers;
    }
    return getDefaultCustomers();
  } catch (error) {
    console.error('Error retrieving customers from local storage:', error);
    return getDefaultCustomers();
  }
};

/**
 * Get default customers (including Walk-in Customer)
 * @returns {Array} Array of default customer objects
 */
export const getDefaultCustomers = () => {
  return [
    {
      id: 'walk-in',
      code: 'WALK-IN',
      name: 'Walk in Customer',
      description: 'Default walk-in customer',
      isTaxExempt: false,
      isPICEditable: true,
      defaultPIC: '',
      accountCode: '',
      remark: '',
      identityTypeId: null,
      identityNo: '',
      fullName: 'Walk in Customer',
      email: '',
      tinNo: '',
      tinVerifyStatus: 0,
      debtorTypeId: null,
      referrerId: null,
      currencyId: null,
      accountGroupId: null,
      companyId: null,
      // Address fields
      firstName: '',
      lastName: '',
      address1: '',
      address2: '',
      address3: '',
      postalCode: '',
      phone: '',
      faxNo: '',
      coordinate: '',
      countryId: null,
      stateProvinceId: null,
      regionId: null,
      // Legacy fields for backward compatibility
      address: '',
      city: '',
      country: '',
      isDefault: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];
};

/**
 * Save customers to local storage
 * @param {Array} customers - Array of customer objects
 */
export const saveCustomers = (customers) => {
  try {
    localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(customers));
  } catch (error) {
    console.error('Error saving customers to local storage:', error);
  }
};

/**
 * Add a new customer - Frontend first approach with background sync
 * @param {Object} customerData - Customer data object
 * @returns {Promise<Object>} The created customer object
 */
export const addCustomer = async (customerData) => {
  try {
    // Validate required fields
    if (!customerData.name || !customerData.name.trim()) {
      throw new Error('Customer name is required');
    }

    // Step 1: Save to frontend storage immediately
    const customers = await getCustomersFromLocalStorage();

    // Generate unique ID and create customer with all backend fields
    const newCustomer = {
      id: generateCustomerId(),
      code: customerData.code || '',
      name: customerData.name.trim(),
      description: customerData.description || '',
      isTaxExempt: customerData.isTaxExempt || false,
      isPICEditable: customerData.isPICEditable !== undefined ? customerData.isPICEditable : true,
      defaultPIC: customerData.defaultPIC || '',
      accountCode: customerData.accountCode || '',
      remark: customerData.remark || '',
      identityTypeId: customerData.identityTypeId || null,
      identityNo: customerData.identityNo || '',
      fullName: customerData.fullName || customerData.name.trim(),
      email: customerData.email || '',
      tinNo: customerData.tinNo || '',
      tinVerifyStatus: customerData.tinVerifyStatus || 0,
      debtorTypeId: customerData.debtorTypeId || null,
      referrerId: customerData.referrerId || null,
      currencyId: customerData.currencyId || null,
      accountGroupId: customerData.accountGroupId || null,
      companyId: customerData.companyId || null,
      // Address fields
      firstName: customerData.firstName || '',
      lastName: customerData.lastName || '',
      address1: customerData.address1 || '',
      address2: customerData.address2 || '',
      address3: customerData.address3 || '',
      postalCode: customerData.postalCode || '',
      phone: customerData.phone || '',
      faxNo: customerData.faxNo || '',
      coordinate: customerData.coordinate || '',
      countryId: customerData.countryId || null,
      stateProvinceId: customerData.stateProvinceId || null,
      regionId: customerData.regionId || null,
      // Legacy fields for backward compatibility
      address: customerData.address1 || customerData.address || '',
      city: customerData.city || '',
      country: customerData.country || '',
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // Sync status fields
      syncStatus: 'pending', // pending, synced, failed
      isLocalOnly: true,
      lastSyncAttempt: null,
      syncError: null
    };

    // Save to frontend storage immediately
    customers.push(newCustomer);
    saveCustomers(customers);

    // Add to pending sync queue
    addToPendingSync(newCustomer.id, 'create', newCustomer);

    // Step 2: Attempt background sync to backend
    syncCustomerToBackend(newCustomer.id, customerData);

    console.log('Customer saved to frontend storage:', newCustomer.name);
    return newCustomer;

  } catch (error) {
    console.error('Error adding customer:', error);
    throw error;
  }
};

/**
 * Update an existing customer - Frontend first approach with background sync
 * @param {string} customerId - Customer ID
 * @param {Object} customerData - Updated customer data
 * @returns {Promise<Object|null>} Updated customer object or null if not found
 */
export const updateCustomer = async (customerId, customerData) => {
  try {
    if (!customerId) {
      throw new Error('Customer ID is required');
    }

    // Get customers from local storage first
    const customers = await getCustomersFromLocalStorage();
    const existingCustomer = customers.find(c => c.id === customerId);

    if (!existingCustomer) {
      console.warn('Customer not found:', customerId);
      return null;
    }

    // Don't allow updating default customer's core properties
    if (existingCustomer.isDefault) {
      console.warn('Cannot update default customer');
      return existingCustomer;
    }

    // Step 1: Update in frontend storage immediately
    const customerIndex = customers.findIndex(c => c.id === customerId);

    if (customerIndex === -1) {
      console.warn('Customer not found in local storage:', customerId);
      return null;
    }

    // Update customer data with sync status
    const updatedCustomer = {
      ...customers[customerIndex],
      ...customerData,
      id: customerId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString(),
      syncStatus: existingCustomer.isLocalOnly ? 'pending' : 'pending', // Mark as pending sync
      lastSyncAttempt: null,
      syncError: null
    };

    customers[customerIndex] = updatedCustomer;
    saveCustomers(customers);

    // Step 2: Add to pending sync queue for backend update
    if (!existingCustomer.isLocalOnly) {
      addToPendingSync(customerId, 'update', updatedCustomer);

      // Attempt background sync
      syncCustomerUpdateToBackend(customerId, customerData);
    }

    // Clear cache to force refresh
    clearCustomersCache();

    console.log('Customer updated in frontend storage:', updatedCustomer.name);
    return updatedCustomer;

  } catch (error) {
    console.error('Error updating customer:', error);
    throw error;
  }
};

/**
 * Delete a customer
 * @param {string} customerId - Customer ID to delete
 * @returns {boolean} True if deleted successfully
 */
export const deleteCustomer = async (customerId) => {
  try {
    const customers = await getCustomersFromLocalStorage();
    const customerIndex = customers.findIndex(customer => customer.id === customerId);

    if (customerIndex === -1) {
      console.warn('Customer not found:', customerId);
      return false;
    }

    // Don't allow deleting default customer
    if (customers[customerIndex].isDefault) {
      console.warn('Cannot delete default customer');
      return false;
    }

    customers.splice(customerIndex, 1);
    saveCustomers(customers);

    // If this was the selected customer, reset to default
    const selectedCustomer = await getSelectedCustomer();
    if (selectedCustomer && selectedCustomer.id === customerId) {
      setSelectedCustomer(customers.find(c => c.isDefault) || customers[0]);
    }

    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
};

/**
 * Get customer by ID
 * @param {string} customerId - Customer ID
 * @returns {Object|null} Customer object or null if not found
 */
export const getCustomerById = async (customerId) => {
  try {
    const customers = await getCustomers();
    return customers.find(customer => customer.id === customerId) || null;
  } catch (error) {
    console.error('Error getting customer by ID:', error);
    return null;
  }
};

/**
 * Get selected customer for current order
 * @returns {Promise<Object|null>} Selected customer object or null
 */
export const getSelectedCustomer = async () => {
  try {
    const selectedCustomer = localStorage.getItem(SELECTED_CUSTOMER_KEY);
    if (selectedCustomer) {
      const parsedCustomer = JSON.parse(selectedCustomer);

      // Clean any nested objects from selected customer
      const cleaned = { ...parsedCustomer };
      let hasNestedObjects = false;
      Object.keys(cleaned).forEach(key => {
        if (typeof cleaned[key] === 'object' && cleaned[key] !== null && !Array.isArray(cleaned[key])) {
          console.warn(`Removing nested object from selected customer.${key}:`, cleaned[key]);
          delete cleaned[key];
          hasNestedObjects = true;
        }
      });

      // Save cleaned data back if needed
      if (hasNestedObjects) {
        console.log('Cleaned selected customer data, saving back to localStorage');
        localStorage.setItem(SELECTED_CUSTOMER_KEY, JSON.stringify(cleaned));
      }

      return cleaned;
    }

    // Return default customer if none selected
    const customers = await getCustomers();
    return customers.find(c => c.isDefault) || customers[0] || null;
  } catch (error) {
    console.error('Error retrieving selected customer:', error);
    // Fallback to local storage
    const localCustomers = getCustomersFromLocalStorage();
    return localCustomers.find(c => c.isDefault) || localCustomers[0] || null;
  }
};

/**
 * Set selected customer for current order
 * @param {Object} customer - Customer object to select
 */
export const setSelectedCustomer = (customer) => {
  try {
    localStorage.setItem(SELECTED_CUSTOMER_KEY, JSON.stringify(customer));
  } catch (error) {
    console.error('Error setting selected customer:', error);
  }
};

/**
 * Clear selected customer (reset to default)
 */
export const clearSelectedCustomer = () => {
  try {
    localStorage.removeItem(SELECTED_CUSTOMER_KEY);
  } catch (error) {
    console.error('Error clearing selected customer:', error);
  }
};

/**
 * Clear customers cache
 */
export const clearCustomersCache = () => {
  try {
    localStorage.removeItem(CUSTOMERS_CACHE_KEY);
    localStorage.removeItem(CACHE_EXPIRY_KEY);
  } catch (error) {
    console.error('Error clearing customers cache:', error);
  }
};

/**
 * Refresh customers from API
 * @returns {Promise<Array>} Array of customer objects
 */
export const refreshCustomers = async () => {
  try {
    clearCustomersCache();
    return await getCustomers(true);
  } catch (error) {
    console.error('Error refreshing customers:', error);
    return getCustomersFromLocalStorage();
  }
};

/**
 * Generate unique customer ID
 * @returns {string} Unique customer ID
 */
const generateCustomerId = () => {
  return 'cust_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Get pending sync queue from local storage
 * @returns {Array} Array of pending sync items
 */
export const getPendingSyncQueue = () => {
  try {
    const pending = localStorage.getItem(PENDING_SYNC_KEY);
    return pending ? JSON.parse(pending) : [];
  } catch (error) {
    console.error('Error retrieving pending sync queue:', error);
    return [];
  }
};

/**
 * Save pending sync queue to local storage
 * @param {Array} queue - Array of pending sync items
 */
export const savePendingSyncQueue = (queue) => {
  try {
    localStorage.setItem(PENDING_SYNC_KEY, JSON.stringify(queue));
  } catch (error) {
    console.error('Error saving pending sync queue:', error);
  }
};

/**
 * Add customer to pending sync queue
 * @param {string} customerId - Customer ID
 * @param {string} operation - Operation type (create, update, delete)
 * @param {Object} customerData - Customer data
 */
export const addToPendingSync = (customerId, operation, customerData) => {
  try {
    const queue = getPendingSyncQueue();

    // Remove existing entry for this customer if any
    const filteredQueue = queue.filter(item => item.customerId !== customerId);

    // Add new entry
    filteredQueue.push({
      customerId,
      operation,
      customerData,
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries: 3
    });

    savePendingSyncQueue(filteredQueue);
    console.log(`Added customer ${customerId} to pending sync queue for ${operation}`);
  } catch (error) {
    console.error('Error adding to pending sync queue:', error);
  }
};

/**
 * Remove customer from pending sync queue
 * @param {string} customerId - Customer ID
 */
export const removeFromPendingSync = (customerId) => {
  try {
    const queue = getPendingSyncQueue();
    const filteredQueue = queue.filter(item => item.customerId !== customerId);
    savePendingSyncQueue(filteredQueue);
    console.log(`Removed customer ${customerId} from pending sync queue`);
  } catch (error) {
    console.error('Error removing from pending sync queue:', error);
  }
};

/**
 * Update customer sync status
 * @param {string} customerId - Customer ID
 * @param {string} status - Sync status (pending, synced, failed)
 * @param {string} error - Error message if failed
 */
export const updateCustomerSyncStatus = async (customerId, status, error = null) => {
  try {
    const customers = await getCustomersFromLocalStorage();
    const customerIndex = customers.findIndex(c => c.id === customerId);

    if (customerIndex !== -1) {
      customers[customerIndex] = {
        ...customers[customerIndex],
        syncStatus: status,
        lastSyncAttempt: new Date().toISOString(),
        syncError: error,
        updatedAt: new Date().toISOString()
      };

      // If successfully synced, remove local-only flag and backend ID
      if (status === 'synced') {
        customers[customerIndex].isLocalOnly = false;
      }

      saveCustomers(customers);
      console.log(`Updated sync status for customer ${customerId}: ${status}`);
    }
  } catch (error) {
    console.error('Error updating customer sync status:', error);
  }
};

/**
 * Sync customer to backend
 * @param {string} customerId - Customer ID
 * @param {Object} customerData - Customer data
 */
export const syncCustomerToBackend = async (customerId, customerData) => {
  try {
    console.log(`Attempting to sync customer ${customerId} to backend...`);

    // Try to create via API
    const response = await customerService.createCustomer(customerData);

    if (response && response.result) {
      // Success - update local customer with backend ID and sync status
      const backendCustomer = customerService.transformToFrontendFormat(response.result);

      const customers = await getCustomersFromLocalStorage();
      const customerIndex = customers.findIndex(c => c.id === customerId);

      if (customerIndex !== -1) {
        // Update with backend data while preserving local ID for consistency
        customers[customerIndex] = {
          ...customers[customerIndex],
          ...backendCustomer,
          id: customerId, // Keep local ID for consistency
          backendId: backendCustomer.id, // Store backend ID separately
          syncStatus: 'synced',
          isLocalOnly: false,
          lastSyncAttempt: new Date().toISOString(),
          syncError: null,
          updatedAt: new Date().toISOString()
        };

        saveCustomers(customers);
        removeFromPendingSync(customerId);

        console.log(`Successfully synced customer ${customerId} to backend`);

        // Trigger customer update event
        window.dispatchEvent(new CustomEvent('customerSynced', {
          detail: { customerId, status: 'synced' }
        }));
      }
    }
  } catch (error) {
    console.warn(`Failed to sync customer ${customerId} to backend:`, error);

    // Update sync status to failed
    await updateCustomerSyncStatus(customerId, 'failed', error.message);

    // Update retry count in pending queue
    const queue = getPendingSyncQueue();
    const queueItem = queue.find(item => item.customerId === customerId);

    if (queueItem) {
      queueItem.retryCount = (queueItem.retryCount || 0) + 1;
      queueItem.lastError = error.message;
      queueItem.lastAttempt = new Date().toISOString();

      // Remove from queue if max retries reached
      if (queueItem.retryCount >= queueItem.maxRetries) {
        console.log(`Max retries reached for customer ${customerId}, removing from sync queue`);
        removeFromPendingSync(customerId);
      } else {
        savePendingSyncQueue(queue);
      }
    }

    // Trigger customer sync failed event
    window.dispatchEvent(new CustomEvent('customerSyncFailed', {
      detail: { customerId, error: error.message }
    }));
  }
};

/**
 * Process pending sync queue - sync all pending customers
 * @returns {Promise<Object>} Sync results summary
 */
export const processPendingSyncQueue = async () => {
  try {
    const queue = getPendingSyncQueue();

    if (queue.length === 0) {
      console.log('No customers pending sync');
      return { total: 0, synced: 0, failed: 0 };
    }

    console.log(`Processing ${queue.length} customers in sync queue...`);

    let synced = 0;
    let failed = 0;

    // Process each item in the queue
    for (const item of queue) {
      try {
        if (item.operation === 'create') {
          await syncCustomerToBackend(item.customerId, item.customerData);
          synced++;
        } else if (item.operation === 'update') {
          await syncCustomerUpdateToBackend(item.customerId, item.customerData);
          synced++;
        }
        // Add support for delete operations in the future
      } catch (error) {
        console.error(`Failed to sync customer ${item.customerId}:`, error);
        failed++;
      }
    }

    const results = { total: queue.length, synced, failed };
    console.log('Sync queue processing completed:', results);

    // Trigger batch sync completed event
    window.dispatchEvent(new CustomEvent('batchSyncCompleted', {
      detail: results
    }));

    return results;
  } catch (error) {
    console.error('Error processing pending sync queue:', error);
    return { total: 0, synced: 0, failed: 0, error: error.message };
  }
};

/**
 * Retry failed syncs for customers that haven't reached max retries
 * @returns {Promise<Object>} Retry results summary
 */
export const retryFailedSyncs = async () => {
  try {
    const queue = getPendingSyncQueue();
    const retryableItems = queue.filter(item =>
      item.retryCount < item.maxRetries &&
      item.lastAttempt &&
      (Date.now() - new Date(item.lastAttempt).getTime()) > 60000 // Wait at least 1 minute between retries
    );

    if (retryableItems.length === 0) {
      console.log('No customers available for retry');
      return { total: 0, retried: 0, failed: 0 };
    }

    console.log(`Retrying sync for ${retryableItems.length} customers...`);

    let retried = 0;
    let failed = 0;

    for (const item of retryableItems) {
      try {
        if (item.operation === 'create') {
          await syncCustomerToBackend(item.customerId, item.customerData);
          retried++;
        } else if (item.operation === 'update') {
          await syncCustomerUpdateToBackend(item.customerId, item.customerData);
          retried++;
        }
      } catch (error) {
        console.error(`Retry failed for customer ${item.customerId}:`, error);
        failed++;
      }
    }

    const results = { total: retryableItems.length, retried, failed };
    console.log('Retry sync completed:', results);

    return results;
  } catch (error) {
    console.error('Error retrying failed syncs:', error);
    return { total: 0, retried: 0, failed: 0, error: error.message };
  }
};

/**
 * Get sync status summary for all customers
 * @returns {Object} Sync status summary
 */
export const getSyncStatusSummary = async () => {
  try {
    const customers = await getCustomersFromLocalStorage();
    const pendingQueue = getPendingSyncQueue();

    const summary = {
      total: customers.length,
      synced: customers.filter(c => c.syncStatus === 'synced').length,
      pending: customers.filter(c => c.syncStatus === 'pending').length,
      failed: customers.filter(c => c.syncStatus === 'failed').length,
      localOnly: customers.filter(c => c.isLocalOnly).length,
      pendingInQueue: pendingQueue.length
    };

    return summary;
  } catch (error) {
    console.error('Error getting sync status summary:', error);
    return { total: 0, synced: 0, pending: 0, failed: 0, localOnly: 0, pendingInQueue: 0 };
  }
};

/**
 * Start periodic sync process
 * @param {number} intervalMinutes - Sync interval in minutes (default: 5)
 */
export const startPeriodicSync = (intervalMinutes = 5) => {
  // Clear any existing interval
  if (window.customerSyncInterval) {
    clearInterval(window.customerSyncInterval);
  }

  console.log(`Starting periodic sync every ${intervalMinutes} minutes`);

  window.customerSyncInterval = setInterval(async () => {
    try {
      console.log('Running periodic sync...');
      await processPendingSyncQueue();
      await retryFailedSyncs();
    } catch (error) {
      console.error('Error in periodic sync:', error);
    }
  }, intervalMinutes * 60 * 1000);
};

/**
 * Stop periodic sync process
 */
export const stopPeriodicSync = () => {
  if (window.customerSyncInterval) {
    clearInterval(window.customerSyncInterval);
    window.customerSyncInterval = null;
    console.log('Stopped periodic sync');
  }
};

/**
 * Force sync a specific customer
 * @param {string} customerId - Customer ID to sync
 * @returns {Promise<boolean>} True if sync successful
 */
export const forceSyncCustomer = async (customerId) => {
  try {
    const customers = await getCustomersFromLocalStorage();
    const customer = customers.find(c => c.id === customerId);

    if (!customer) {
      throw new Error('Customer not found');
    }

    if (customer.syncStatus === 'synced' && !customer.isLocalOnly) {
      console.log('Customer already synced');
      return true;
    }

    // Determine operation type based on customer state
    const operation = customer.isLocalOnly ? 'create' : 'update';

    // Add to pending sync if not already there
    addToPendingSync(customerId, operation, customer);

    // Attempt sync based on operation
    if (operation === 'create') {
      await syncCustomerToBackend(customerId, customer);
    } else {
      await syncCustomerUpdateToBackend(customerId, customer);
    }

    return true;
  } catch (error) {
    console.error(`Error force syncing customer ${customerId}:`, error);
    return false;
  }
};

/**
 * Sync customer update to backend
 * @param {string} customerId - Customer ID
 * @param {Object} customerData - Updated customer data
 */
export const syncCustomerUpdateToBackend = async (customerId, customerData) => {
  try {
    console.log(`Attempting to sync customer update ${customerId} to backend...`);

    const customers = await getCustomersFromLocalStorage();
    const customer = customers.find(c => c.id === customerId);

    if (!customer) {
      throw new Error('Customer not found');
    }

    // Use backend ID if available, otherwise use local ID
    const backendId = customer.backendId || customer.id;

    // Try to update via API
    const response = await customerService.updateCustomer(backendId, customerData);

    if (response && response.isSuccessful) {
      // Success - update sync status
      await updateCustomerSyncStatus(customerId, 'synced');
      removeFromPendingSync(customerId);

      console.log(`Successfully synced customer update ${customerId} to backend`);

      // Trigger customer update synced event
      window.dispatchEvent(new CustomEvent('customerUpdateSynced', {
        detail: { customerId, status: 'synced' }
      }));
    }
  } catch (error) {
    console.warn(`Failed to sync customer update ${customerId} to backend:`, error);

    // Update sync status to failed
    await updateCustomerSyncStatus(customerId, 'failed', error.message);

    // Update retry count in pending queue
    const queue = getPendingSyncQueue();
    const queueItem = queue.find(item => item.customerId === customerId);

    if (queueItem) {
      queueItem.retryCount = (queueItem.retryCount || 0) + 1;
      queueItem.lastError = error.message;
      queueItem.lastAttempt = new Date().toISOString();

      // Remove from queue if max retries reached
      if (queueItem.retryCount >= queueItem.maxRetries) {
        console.log(`Max retries reached for customer update ${customerId}, removing from sync queue`);
        removeFromPendingSync(customerId);
      } else {
        savePendingSyncQueue(queue);
      }
    }

    // Trigger customer update sync failed event
    window.dispatchEvent(new CustomEvent('customerUpdateSyncFailed', {
      detail: { customerId, error: error.message }
    }));
  }
};

/**
 * Clear all sync data (for testing/reset purposes)
 */
export const clearSyncData = () => {
  try {
    localStorage.removeItem(PENDING_SYNC_KEY);
    localStorage.removeItem(SYNC_STATUS_KEY);
    console.log('Cleared all sync data');
  } catch (error) {
    console.error('Error clearing sync data:', error);
  }
};

/**
 * Get customers formatted for Select component
 * @param {boolean} useCache - Whether to use cached data
 * @returns {Promise<Array>} Array of options for Select component
 */
export const getCustomersForSelect = async (useCache = true) => {
  try {
    const customers = useCache ? await getCustomers() : await getCustomers(true);
    return customers.map(customer => ({
      value: customer.id,
      label: customer.name,
      customer: customer
    }));
  } catch (error) {
    console.error('Error formatting customers for select:', error);
    // Fallback to local storage
    const localCustomers = getCustomersFromLocalStorage();
    return localCustomers.map(customer => ({
      value: customer.id,
      label: customer.name,
      customer: customer
    }));
  }
};

/**
 * Search customers by name, phone, or email
 * @param {string} searchTerm - Search term
 * @param {boolean} useApi - Whether to use API search
 * @returns {Promise<Array>} Array of matching customers
 */
export const searchCustomers = async (searchTerm, useApi = true) => {
  try {
    if (!searchTerm) return await getCustomers();

    // If API search is enabled and search term is provided, try API first
    if (useApi && searchTerm.trim()) {
      try {
        const response = await customerService.searchCustomers(searchTerm.trim());
        if (response && response.result) {
          const customers = response.result.map(customer => {
            const transformedCustomer = customerService.transformToFrontendFormat(customer);

            // Debug: Check for nested objects in search results
            Object.keys(transformedCustomer).forEach(key => {
              if (typeof transformedCustomer[key] === 'object' && transformedCustomer[key] !== null && !Array.isArray(transformedCustomer[key])) {
                console.warn(`Removing nested object from search result: ${key}`, transformedCustomer[key]);
                delete transformedCustomer[key];
              }
            });

            return transformedCustomer;
          });

          // Always include default customers
          const defaultCustomers = getDefaultCustomers();
          return [...defaultCustomers, ...customers];
        }
      } catch (apiError) {
        console.warn('API search failed, falling back to local search:', apiError);
      }
    }

    // Fallback to local search
    const customers = await getCustomers();
    const term = searchTerm.toLowerCase();

    return customers.filter(customer =>
      customer.name.toLowerCase().includes(term) ||
      customer.phone.includes(term) ||
      customer.email.toLowerCase().includes(term)
    );
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
};

/**
 * Get all customer orders from local storage
 * @returns {Array} Array of customer order objects
 */
export const getCustomerOrders = () => {
  try {
    const orders = localStorage.getItem(CUSTOMER_ORDERS_KEY);
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error retrieving customer orders from local storage:', error);
    return [];
  }
};

/**
 * Save customer orders to local storage
 * @param {Array} orders - Array of customer order objects
 */
export const saveCustomerOrders = (orders) => {
  try {
    localStorage.setItem(CUSTOMER_ORDERS_KEY, JSON.stringify(orders));
  } catch (error) {
    console.error('Error saving customer orders to local storage:', error);
  }
};

/**
 * Save order for a specific customer
 * @param {Object} orderData - Order data object
 * @returns {Object} The saved order object
 */
export const saveOrderForCustomer = (orderData) => {
  try {
    const customerOrders = getCustomerOrders();

    // Generate unique order ID
    const orderId = generateOrderId();

    const newOrder = {
      id: orderId,
      customerId: orderData.customerId,
      customerName: orderData.customerName,
      orderItems: orderData.orderItems || [],
      orderTotal: orderData.orderTotal || {},
      discountSettings: orderData.discountSettings || {},
      paymentSummary: orderData.paymentSummary || {},
      payments: orderData.payments || [],
      status: orderData.status || 'saved',
      reference: generateOrderReference(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: orderData.notes || ''
    };

    customerOrders.push(newOrder);
    saveCustomerOrders(customerOrders);

    return newOrder;
  } catch (error) {
    console.error('Error saving order for customer:', error);
    throw error;
  }
};

/**
 * Get orders for a specific customer
 * @param {string} customerId - Customer ID
 * @returns {Array} Array of orders for the customer
 */
export const getOrdersForCustomer = (customerId) => {
  try {
    const customerOrders = getCustomerOrders();
    return customerOrders.filter(order => order.customerId === customerId);
  } catch (error) {
    console.error('Error getting orders for customer:', error);
    return [];
  }
};

/**
 * Load a saved order
 * @param {string} orderId - Order ID to load
 * @returns {Object|null} Order object or null if not found
 */
export const loadSavedOrder = (orderId) => {
  try {
    const customerOrders = getCustomerOrders();
    return customerOrders.find(order => order.id === orderId) || null;
  } catch (error) {
    console.error('Error loading saved order:', error);
    return null;
  }
};

/**
 * Update a saved order
 * @param {string} orderId - Order ID to update
 * @param {Object} orderData - Updated order data
 * @returns {Object|null} Updated order object or null if not found
 */
export const updateSavedOrder = (orderId, orderData) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return null;
    }

    customerOrders[orderIndex] = {
      ...customerOrders[orderIndex],
      ...orderData,
      id: orderId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    saveCustomerOrders(customerOrders);
    return customerOrders[orderIndex];
  } catch (error) {
    console.error('Error updating saved order:', error);
    throw error;
  }
};

/**
 * Delete a saved order
 * @param {string} orderId - Order ID to delete
 * @returns {boolean} True if deleted successfully
 */
export const deleteSavedOrder = (orderId) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return false;
    }

    customerOrders.splice(orderIndex, 1);
    saveCustomerOrders(customerOrders);

    return true;
  } catch (error) {
    console.error('Error deleting saved order:', error);
    return false;
  }
};

/**
 * Complete a saved order (mark as completed)
 * @param {string} orderId - Order ID to complete
 * @param {Object} completionData - Additional completion data
 * @returns {Object|null} Completed order object or null if not found
 */
export const completeSavedOrder = (orderId, completionData = {}) => {
  try {
    const customerOrders = getCustomerOrders();
    const orderIndex = customerOrders.findIndex(order => order.id === orderId);

    if (orderIndex === -1) {
      console.warn('Order not found:', orderId);
      return null;
    }

    customerOrders[orderIndex] = {
      ...customerOrders[orderIndex],
      ...completionData,
      status: 'completed',
      completedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    saveCustomerOrders(customerOrders);
    return customerOrders[orderIndex];
  } catch (error) {
    console.error('Error completing saved order:', error);
    throw error;
  }
};

/**
 * Generate unique order ID
 * @returns {string} Unique order ID
 */
const generateOrderId = () => {
  return 'order_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Generate order reference number
 * @returns {string} Order reference number
 */
const generateOrderReference = () => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 3).toUpperCase();
  return `ORD${timestamp}${random}`;
};

/**
 * Initialize customer sync system
 * Call this function on app startup
 */
export const initializeCustomerSync = () => {
  try {
    console.log('Initializing customer sync system...');

    // Clean existing data
    cleanAllCustomerData();

    // Start periodic sync (every 5 minutes)
    startPeriodicSync(5);

    // Process any pending syncs immediately
    setTimeout(() => {
      processPendingSyncQueue();
    }, 2000); // Wait 2 seconds after app start

    console.log('Customer sync system initialized');
  } catch (error) {
    console.error('Error initializing customer sync system:', error);
  }
};

/**
 * Clean all customer data to remove nested objects
 * Call this function on app startup to ensure data integrity
 */
export const cleanAllCustomerData = () => {
  try {
    console.log('Cleaning all customer data...');

    // Clean customers data
    const customersData = localStorage.getItem(CUSTOMERS_STORAGE_KEY);
    if (customersData) {
      const customers = JSON.parse(customersData);
      const cleanedCustomers = customers.map(customer => {
        const cleaned = { ...customer };
        Object.keys(cleaned).forEach(key => {
          if (typeof cleaned[key] === 'object' && cleaned[key] !== null && !Array.isArray(cleaned[key])) {
            console.warn(`Removing nested object from customer.${key}`);
            delete cleaned[key];
          }
        });

        // Ensure sync status fields exist
        if (!cleaned.syncStatus) {
          cleaned.syncStatus = cleaned.isLocalOnly ? 'pending' : 'synced';
        }

        return cleaned;
      });
      localStorage.setItem(CUSTOMERS_STORAGE_KEY, JSON.stringify(cleanedCustomers));
    }

    // Clean selected customer data
    const selectedCustomerData = localStorage.getItem(SELECTED_CUSTOMER_KEY);
    if (selectedCustomerData) {
      const selectedCustomer = JSON.parse(selectedCustomerData);
      const cleaned = { ...selectedCustomer };
      Object.keys(cleaned).forEach(key => {
        if (typeof cleaned[key] === 'object' && cleaned[key] !== null && !Array.isArray(cleaned[key])) {
          console.warn(`Removing nested object from selected customer.${key}`);
          delete cleaned[key];
        }
      });
      localStorage.setItem(SELECTED_CUSTOMER_KEY, JSON.stringify(cleaned));
    }

    // Clear cache to force refresh
    clearCustomersCache();

    console.log('Customer data cleaning completed');
  } catch (error) {
    console.error('Error cleaning customer data:', error);
  }
};
