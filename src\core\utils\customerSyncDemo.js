/**
 * Demo script to showcase the new frontend-first customer sync functionality
 * This file demonstrates how the new customer creation and sync system works
 */

import { 
  addCustomer, 
  updateCustomer,
  getSyncStatusSummary,
  processPendingSyncQueue,
  forceSyncCustomer,
  getPendingSyncQueue,
  initializeCustomerSync,
  clearSyncData
} from './customerStorage';

/**
 * Demo: Create a new customer with frontend-first approach
 */
export const demoCreateCustomer = async () => {
  console.log('=== Demo: Frontend-First Customer Creation ===');
  
  try {
    // Create a new customer - this will save to frontend immediately
    const newCustomer = await addCustomer({
      name: 'Demo Customer',
      email: '<EMAIL>',
      phone: '+60123456789',
      address1: '123 Demo Street',
      address2: 'Demo Area',
      postalCode: '12345',
      city: 'Demo City',
      description: 'Demo customer for testing sync functionality'
    });
    
    console.log('✅ Customer created and saved to frontend storage:', newCustomer.name);
    console.log('📊 Customer ID:', newCustomer.id);
    console.log('🔄 Sync Status:', newCustomer.syncStatus);
    console.log('📱 Local Only:', newCustomer.isLocalOnly);
    
    // Show sync status
    const syncStatus = await getSyncStatusSummary();
    console.log('📈 Current Sync Status:', syncStatus);
    
    return newCustomer;
  } catch (error) {
    console.error('❌ Error creating customer:', error);
    throw error;
  }
};

/**
 * Demo: Update an existing customer
 */
export const demoUpdateCustomer = async (customerId) => {
  console.log('=== Demo: Frontend-First Customer Update ===');
  
  try {
    const updatedCustomer = await updateCustomer(customerId, {
      email: '<EMAIL>',
      phone: '+60987654321',
      description: 'Updated demo customer'
    });
    
    console.log('✅ Customer updated in frontend storage:', updatedCustomer.name);
    console.log('🔄 Sync Status:', updatedCustomer.syncStatus);
    
    return updatedCustomer;
  } catch (error) {
    console.error('❌ Error updating customer:', error);
    throw error;
  }
};

/**
 * Demo: Check sync status and pending queue
 */
export const demoCheckSyncStatus = async () => {
  console.log('=== Demo: Check Sync Status ===');
  
  try {
    const syncStatus = await getSyncStatusSummary();
    console.log('📈 Sync Status Summary:');
    console.log('  - Total Customers:', syncStatus.total);
    console.log('  - Synced:', syncStatus.synced);
    console.log('  - Pending:', syncStatus.pending);
    console.log('  - Failed:', syncStatus.failed);
    console.log('  - Local Only:', syncStatus.localOnly);
    console.log('  - In Sync Queue:', syncStatus.pendingInQueue);
    
    const pendingQueue = getPendingSyncQueue();
    console.log('📋 Pending Sync Queue:');
    pendingQueue.forEach((item, index) => {
      console.log(`  ${index + 1}. Customer: ${item.customerData.name}`);
      console.log(`     Operation: ${item.operation}`);
      console.log(`     Retry Count: ${item.retryCount}/${item.maxRetries}`);
      console.log(`     Timestamp: ${item.timestamp}`);
    });
    
    return { syncStatus, pendingQueue };
  } catch (error) {
    console.error('❌ Error checking sync status:', error);
    throw error;
  }
};

/**
 * Demo: Process pending sync queue
 */
export const demoProcessSyncQueue = async () => {
  console.log('=== Demo: Process Sync Queue ===');
  
  try {
    console.log('🔄 Processing pending sync queue...');
    const results = await processPendingSyncQueue();
    
    console.log('✅ Sync queue processing completed:');
    console.log('  - Total Processed:', results.total);
    console.log('  - Successfully Synced:', results.synced);
    console.log('  - Failed:', results.failed);
    
    if (results.error) {
      console.log('  - Error:', results.error);
    }
    
    return results;
  } catch (error) {
    console.error('❌ Error processing sync queue:', error);
    throw error;
  }
};

/**
 * Demo: Force sync a specific customer
 */
export const demoForceSyncCustomer = async (customerId) => {
  console.log('=== Demo: Force Sync Customer ===');
  
  try {
    console.log(`🔄 Force syncing customer: ${customerId}`);
    const success = await forceSyncCustomer(customerId);
    
    if (success) {
      console.log('✅ Customer force sync completed successfully');
    } else {
      console.log('❌ Customer force sync failed');
    }
    
    return success;
  } catch (error) {
    console.error('❌ Error force syncing customer:', error);
    throw error;
  }
};

/**
 * Demo: Complete workflow demonstration
 */
export const demoCompleteWorkflow = async () => {
  console.log('=== Demo: Complete Frontend-First Sync Workflow ===');
  
  try {
    // Step 1: Initialize sync system
    console.log('🚀 Initializing sync system...');
    initializeCustomerSync();
    
    // Step 2: Create a customer
    console.log('\n📝 Creating new customer...');
    const customer = await demoCreateCustomer();
    
    // Step 3: Check initial sync status
    console.log('\n📊 Checking initial sync status...');
    await demoCheckSyncStatus();
    
    // Step 4: Update the customer
    console.log('\n✏️ Updating customer...');
    await demoUpdateCustomer(customer.id);
    
    // Step 5: Check sync status after update
    console.log('\n📊 Checking sync status after update...');
    await demoCheckSyncStatus();
    
    // Step 6: Process sync queue
    console.log('\n🔄 Processing sync queue...');
    await demoProcessSyncQueue();
    
    // Step 7: Final sync status
    console.log('\n📊 Final sync status...');
    await demoCheckSyncStatus();
    
    console.log('\n🎉 Demo completed successfully!');
    
  } catch (error) {
    console.error('❌ Demo workflow failed:', error);
    throw error;
  }
};

/**
 * Demo: Reset sync data for testing
 */
export const demoResetSyncData = () => {
  console.log('=== Demo: Reset Sync Data ===');
  
  try {
    clearSyncData();
    console.log('✅ Sync data cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing sync data:', error);
    throw error;
  }
};

/**
 * Demo: Simulate offline/online scenarios
 */
export const demoOfflineScenario = async () => {
  console.log('=== Demo: Offline Scenario ===');
  
  try {
    console.log('📱 Simulating offline customer creation...');
    
    // Create customers while "offline" (API will fail)
    const customer1 = await addCustomer({
      name: 'Offline Customer 1',
      email: '<EMAIL>',
      phone: '+60111111111'
    });
    
    const customer2 = await addCustomer({
      name: 'Offline Customer 2',
      email: '<EMAIL>',
      phone: '+60222222222'
    });
    
    console.log('✅ Created customers while offline');
    console.log('📊 Customers saved to frontend storage');
    
    // Check sync status
    await demoCheckSyncStatus();
    
    console.log('\n🌐 Simulating coming back online...');
    console.log('🔄 Processing pending syncs...');
    
    // Process pending syncs when back online
    await demoProcessSyncQueue();
    
    // Final status
    await demoCheckSyncStatus();
    
    return { customer1, customer2 };
  } catch (error) {
    console.error('❌ Error in offline scenario demo:', error);
    throw error;
  }
};

// Export all demo functions for easy access
export default {
  demoCreateCustomer,
  demoUpdateCustomer,
  demoCheckSyncStatus,
  demoProcessSyncQueue,
  demoForceSyncCustomer,
  demoCompleteWorkflow,
  demoResetSyncData,
  demoOfflineScenario
};
