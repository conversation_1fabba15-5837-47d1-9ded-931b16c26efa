/**
 * Debug helper functions for troubleshooting customer issues
 * These functions can be called from the browser console to diagnose problems
 */

import { 
  debugCustomerStorage,
  getCustomersFromLocalStorage,
  getCustomersForSelect,
  addCustomer,
  clearCustomersCache,
  refreshCustomers
} from './customerStorage';

/**
 * Test customer creation and check if it appears in the dropdown
 */
window.testCustomerCreation = async () => {
  console.log('🧪 Testing customer creation...');
  
  try {
    // Step 1: Check initial state
    console.log('\n📊 Initial state:');
    await debugCustomerStorage();
    
    // Step 2: Get initial customer count for select
    const initialCustomers = await getCustomersForSelect(false);
    console.log(`\n📋 Initial customers for select: ${initialCustomers.length}`);
    
    // Step 3: Create a test customer
    console.log('\n➕ Creating test customer...');
    const testCustomer = await addCustomer({
      name: 'Test Customer ' + Date.now(),
      email: '<EMAIL>',
      phone: '+60123456789',
      address1: 'Test Address'
    });
    
    console.log('✅ Test customer created:', testCustomer);
    
    // Step 4: Check state after creation
    console.log('\n📊 State after creation:');
    await debugCustomerStorage();
    
    // Step 5: Get customers for select again
    const updatedCustomers = await getCustomersForSelect(false);
    console.log(`\n📋 Updated customers for select: ${updatedCustomers.length}`);
    
    // Step 6: Check if our customer is in the list
    const foundCustomer = updatedCustomers.find(c => c.customer.id === testCustomer.id);
    if (foundCustomer) {
      console.log('✅ Customer found in dropdown list:', foundCustomer.label);
    } else {
      console.log('❌ Customer NOT found in dropdown list');
      console.log('Available customers:', updatedCustomers.map(c => c.label));
    }
    
    return {
      testCustomer,
      initialCount: initialCustomers.length,
      updatedCount: updatedCustomers.length,
      found: !!foundCustomer
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { error: error.message };
  }
};

/**
 * Check current customer storage state
 */
window.checkCustomerState = async () => {
  console.log('🔍 Checking customer storage state...');
  return await debugCustomerStorage();
};

/**
 * Force refresh customer cache and check
 */
window.forceRefreshCustomers = async () => {
  console.log('🔄 Force refreshing customers...');
  
  try {
    // Clear cache
    clearCustomersCache();
    console.log('✅ Cache cleared');
    
    // Refresh customers
    const customers = await refreshCustomers();
    console.log(`✅ Refreshed ${customers.length} customers`);
    
    // Get for select
    const selectCustomers = await getCustomersForSelect(false);
    console.log(`✅ Got ${selectCustomers.length} customers for select`);
    
    return {
      totalCustomers: customers.length,
      selectCustomers: selectCustomers.length,
      customers: selectCustomers.map(c => ({ id: c.value, name: c.label }))
    };
    
  } catch (error) {
    console.error('❌ Refresh failed:', error);
    return { error: error.message };
  }
};

/**
 * Search for a specific customer by name
 */
window.findCustomer = async (customerName) => {
  console.log(`🔍 Searching for customer: ${customerName}`);
  
  try {
    // Check local storage
    const localCustomers = getCustomersFromLocalStorage();
    const localFound = localCustomers.find(c => 
      c.name.toLowerCase().includes(customerName.toLowerCase())
    );
    
    // Check select options
    const selectCustomers = await getCustomersForSelect(false);
    const selectFound = selectCustomers.find(c => 
      c.label.toLowerCase().includes(customerName.toLowerCase())
    );
    
    console.log('Local storage result:', localFound || 'Not found');
    console.log('Select options result:', selectFound || 'Not found');
    
    return {
      localFound: !!localFound,
      selectFound: !!selectFound,
      localCustomer: localFound,
      selectCustomer: selectFound
    };
    
  } catch (error) {
    console.error('❌ Search failed:', error);
    return { error: error.message };
  }
};

/**
 * List all customers in different storage locations
 */
window.listAllCustomers = async () => {
  console.log('📋 Listing all customers...');
  
  try {
    // Local storage customers
    const localCustomers = getCustomersFromLocalStorage();
    console.log('\n📁 Local Storage Customers:');
    localCustomers.forEach((customer, index) => {
      console.log(`  ${index + 1}. ${customer.name} (ID: ${customer.id})`);
    });
    
    // Select customers
    const selectCustomers = await getCustomersForSelect(false);
    console.log('\n📋 Select Dropdown Customers:');
    selectCustomers.forEach((customer, index) => {
      console.log(`  ${index + 1}. ${customer.label} (ID: ${customer.value})`);
    });
    
    return {
      localCount: localCustomers.length,
      selectCount: selectCustomers.length,
      localCustomers: localCustomers.map(c => ({ id: c.id, name: c.name })),
      selectCustomers: selectCustomers.map(c => ({ id: c.value, name: c.label }))
    };
    
  } catch (error) {
    console.error('❌ Listing failed:', error);
    return { error: error.message };
  }
};

/**
 * Trigger customer update event manually
 */
window.triggerCustomerUpdate = (customer) => {
  console.log('📡 Triggering customer update event...');
  
  window.dispatchEvent(new CustomEvent('customerUpdated', {
    detail: { customer }
  }));
  
  console.log('✅ Event triggered');
};

// Export functions for use in other modules
export {
  testCustomerCreation: window.testCustomerCreation,
  checkCustomerState: window.checkCustomerState,
  forceRefreshCustomers: window.forceRefreshCustomers,
  findCustomer: window.findCustomer,
  listAllCustomers: window.listAllCustomers,
  triggerCustomerUpdate: window.triggerCustomerUpdate
};

// Log available debug functions
console.log('🛠️ Customer Debug Helper loaded. Available functions:');
console.log('  - testCustomerCreation() - Test creating a customer and check if it appears');
console.log('  - checkCustomerState() - Check current storage state');
console.log('  - forceRefreshCustomers() - Force refresh customer cache');
console.log('  - findCustomer("name") - Search for a specific customer');
console.log('  - listAllCustomers() - List all customers in storage');
console.log('  - triggerCustomerUpdate(customer) - Manually trigger update event');
