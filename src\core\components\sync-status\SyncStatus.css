/* Sync Status Component Styles */

.sync-status {
  font-size: 0.875rem;
}

.sync-indicator {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.sync-indicator i {
  font-size: 1rem;
}

.sync-indicator i.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.sync-summary {
  padding: 0.5rem;
  background-color: var(--bs-light);
  border-radius: 0.375rem;
  border: 1px solid var(--bs-border-color);
}

.sync-summary .row {
  margin: 0;
}

.sync-summary .col-auto {
  padding: 0 0.25rem;
}

.sync-summary small {
  display: block;
  line-height: 1.2;
}

/* Status Colors */
.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-info {
  color: #0dcaf0 !important;
}

.text-secondary {
  color: #6c757d !important;
}

/* <PERSON><PERSON> */
.alert-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

/* Button Styles */
.btn-outline-primary {
  border-color: #0d6efd;
  color: #0d6efd;
}

.btn-outline-primary:hover {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
}

.btn-outline-primary:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 576px) {
  .sync-status {
    font-size: 0.8125rem;
  }
  
  .sync-summary {
    padding: 0.375rem;
  }
  
  .sync-summary .col-auto {
    padding: 0 0.125rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .sync-summary {
    background-color: var(--bs-dark);
    border-color: var(--bs-border-color-dark);
  }
}

/* Customer Sync Status Badge */
.customer-sync-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid;
}

.customer-sync-badge.synced {
  background-color: #d1e7dd;
  border-color: #badbcc;
  color: #0f5132;
}

.customer-sync-badge.pending {
  background-color: #fff3cd;
  border-color: #ffecb5;
  color: #664d03;
}

.customer-sync-badge.failed {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

.customer-sync-badge.local-only {
  background-color: #cff4fc;
  border-color: #b6effb;
  color: #055160;
}

.customer-sync-badge i {
  margin-right: 0.25rem;
  font-size: 0.875rem;
}

/* Sync Progress Indicator */
.sync-progress {
  position: relative;
  height: 4px;
  background-color: var(--bs-gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.sync-progress-bar {
  height: 100%;
  background-color: var(--bs-primary);
  transition: width 0.3s ease;
}

.sync-progress-bar.indeterminate {
  width: 30%;
  animation: indeterminate 2s infinite linear;
}

@keyframes indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* Tooltip Styles */
.sync-tooltip {
  position: relative;
  cursor: help;
}

.sync-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #000;
  color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 0.25rem;
}

.sync-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #000;
  z-index: 1000;
}
