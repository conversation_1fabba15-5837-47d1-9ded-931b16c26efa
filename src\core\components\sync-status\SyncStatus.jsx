import React, { useState, useEffect } from 'react';
import { 
  getSyncStatusSummary, 
  processPendingSyncQueue, 
  retryFailedSyncs,
  forceSyncCustomer 
} from '../../utils/customerStorage';

const SyncStatus = ({ customerId = null, showSummary = true, showControls = true }) => {
  const [syncSummary, setSyncSummary] = useState({
    total: 0,
    synced: 0,
    pending: 0,
    failed: 0,
    localOnly: 0,
    pendingInQueue: 0
  });
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);

  // Load sync status
  const loadSyncStatus = async () => {
    try {
      const summary = await getSyncStatusSummary();
      setSyncSummary(summary);
    } catch (error) {
      console.error('Error loading sync status:', error);
    }
  };

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Listen for sync events
  useEffect(() => {
    const handleSyncEvents = () => {
      loadSyncStatus();
      setLastSyncTime(new Date());
    };

    window.addEventListener('customerSynced', handleSyncEvents);
    window.addEventListener('customerUpdateSynced', handleSyncEvents);
    window.addEventListener('batchSyncCompleted', handleSyncEvents);

    return () => {
      window.removeEventListener('customerSynced', handleSyncEvents);
      window.removeEventListener('customerUpdateSynced', handleSyncEvents);
      window.removeEventListener('batchSyncCompleted', handleSyncEvents);
    };
  }, []);

  // Load initial status
  useEffect(() => {
    loadSyncStatus();
  }, []);

  // Handle manual sync
  const handleManualSync = async () => {
    if (!isOnline) {
      alert('Cannot sync while offline. Please check your internet connection.');
      return;
    }

    setIsSyncing(true);
    try {
      if (customerId) {
        // Sync specific customer
        await forceSyncCustomer(customerId);
      } else {
        // Sync all pending customers
        await processPendingSyncQueue();
        await retryFailedSyncs();
      }
      await loadSyncStatus();
      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Error during manual sync:', error);
      alert('Sync failed. Please try again.');
    } finally {
      setIsSyncing(false);
    }
  };

  // Get sync status color
  const getSyncStatusColor = () => {
    if (!isOnline) return 'text-secondary';
    if (syncSummary.failed > 0) return 'text-danger';
    if (syncSummary.pending > 0 || syncSummary.pendingInQueue > 0) return 'text-warning';
    return 'text-success';
  };

  // Get sync status icon
  const getSyncStatusIcon = () => {
    if (!isOnline) return 'ti ti-wifi-off';
    if (isSyncing) return 'ti ti-loader';
    if (syncSummary.failed > 0) return 'ti ti-alert-circle';
    if (syncSummary.pending > 0 || syncSummary.pendingInQueue > 0) return 'ti ti-clock';
    return 'ti ti-check-circle';
  };

  // Get sync status text
  const getSyncStatusText = () => {
    if (!isOnline) return 'Offline';
    if (isSyncing) return 'Syncing...';
    if (syncSummary.failed > 0) return `${syncSummary.failed} Failed`;
    if (syncSummary.pending > 0 || syncSummary.pendingInQueue > 0) {
      return `${syncSummary.pending + syncSummary.pendingInQueue} Pending`;
    }
    return 'All Synced';
  };

  return (
    <div className="sync-status">
      {/* Compact Status Indicator */}
      <div className="d-flex align-items-center gap-2">
        <div className={`sync-indicator ${getSyncStatusColor()}`}>
          <i className={`${getSyncStatusIcon()} ${isSyncing ? 'spin' : ''}`}></i>
          <span className="ms-1">{getSyncStatusText()}</span>
        </div>

        {showControls && (
          <button
            type="button"
            className="btn btn-sm btn-outline-primary"
            onClick={handleManualSync}
            disabled={isSyncing || !isOnline}
            title={customerId ? 'Sync this customer' : 'Sync all pending customers'}
          >
            {isSyncing ? (
              <>
                <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                Syncing...
              </>
            ) : (
              <>
                <i className="ti ti-refresh me-1"></i>
                Sync {customerId ? 'Customer' : 'All'}
              </>
            )}
          </button>
        )}
      </div>

      {/* Detailed Summary */}
      {showSummary && syncSummary.total > 0 && (
        <div className="sync-summary mt-2">
          <div className="row g-2">
            <div className="col-auto">
              <small className="text-muted">
                Total: <span className="fw-bold">{syncSummary.total}</span>
              </small>
            </div>
            <div className="col-auto">
              <small className="text-success">
                Synced: <span className="fw-bold">{syncSummary.synced}</span>
              </small>
            </div>
            {syncSummary.pending > 0 && (
              <div className="col-auto">
                <small className="text-warning">
                  Pending: <span className="fw-bold">{syncSummary.pending}</span>
                </small>
              </div>
            )}
            {syncSummary.failed > 0 && (
              <div className="col-auto">
                <small className="text-danger">
                  Failed: <span className="fw-bold">{syncSummary.failed}</span>
                </small>
              </div>
            )}
            {syncSummary.localOnly > 0 && (
              <div className="col-auto">
                <small className="text-info">
                  Local Only: <span className="fw-bold">{syncSummary.localOnly}</span>
                </small>
              </div>
            )}
          </div>
          
          {lastSyncTime && (
            <div className="mt-1">
              <small className="text-muted">
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </small>
            </div>
          )}
        </div>
      )}

      {/* Offline Notice */}
      {!isOnline && (
        <div className="alert alert-warning alert-sm mt-2 mb-0">
          <i className="ti ti-wifi-off me-1"></i>
          You're offline. Changes will sync when connection is restored.
        </div>
      )}
    </div>
  );
};

export default SyncStatus;
